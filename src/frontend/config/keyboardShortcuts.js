export const SHORTCUTS = {
  send: { key: 'Enter', ctrl: true }, // Ctrl/Cmd + Enter
  newline: { key: 'Enter', shift: true }, // Shift + Enter
  clearHighlight: { key: 'Escape' },
  navUp: { key: 'ArrowUp' },
  navDown: { key: 'ArrowDown' }
};

// Helper to match a KeyboardEvent against a shortcut definition.
// shortcutDef can be a string key (e.g. 'Escape') or an object as above.
export function matchesShortcut(event, shortcutDef) {
  if (!event || !shortcutDef) return false;

  if (typeof shortcutDef === 'string') {
    return event.key === shortcutDef;
  }

  const keyMatch = event.key === shortcutDef.key;
  if (!keyMatch) return false;

  // if ctrl is specified, require ctrlKey or metaKey (for macOS Cmd)
  if (shortcutDef.ctrl === true) {
    if (!(event.ctrlKey || event.metaKey)) return false;
  }
  if (shortcutDef.ctrl === false) {
    if (event.ctrlKey || event.metaKey) return false;
  }

  if (shortcutDef.shift === true && !event.shiftKey) return false;
  if (shortcutDef.shift === false && event.shiftKey) return false;

  // other modifiers could be added if needed
  return true;
}

export default {
  SHORTCUTS,
  matchesShortcut
};