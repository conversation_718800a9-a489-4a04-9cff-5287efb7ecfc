import React, { useState } from 'react';

export default function SummaryPane({ summaries = [], onSelect = () => {} }) {
  const [hoveredSummary, setHoveredSummary] = useState(null);

  // Parse summary content to extract user/AI dialogue
  const parseSummaryContent = (summary) => {
    const content = summary.text || summary.summary || '';

    // Try to parse structured dialogue format
    try {
      // Check if content is JSON-like with user/assistant structure
      if (content.includes('"user"') && content.includes('"assistant"')) {
        const parsed = JSON.parse(content);
        if (parsed.user || parsed.assistant) {
          return {
            type: 'dialogue',
            user: parsed.user || '',
            assistant: parsed.assistant || ''
          };
        }
      }
    } catch (e) {
      // Not JSON, continue with text parsing
    }

    // Try to parse text-based dialogue format
    const lines = content.split('\n').filter(line => line.trim());
    const dialogue = { user: '', assistant: '' };
    let currentSpeaker = null;

    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('用户:') || trimmed.startsWith('User:')) {
        currentSpeaker = 'user';
        dialogue.user += trimmed.substring(trimmed.indexOf(':') + 1).trim() + '\n';
      } else if (trimmed.startsWith('助手:') || trimmed.startsWith('Assistant:') || trimmed.startsWith('AI:')) {
        currentSpeaker = 'assistant';
        dialogue.assistant += trimmed.substring(trimmed.indexOf(':') + 1).trim() + '\n';
      } else if (currentSpeaker && trimmed) {
        dialogue[currentSpeaker] += trimmed + '\n';
      }
    }

    if (dialogue.user.trim() || dialogue.assistant.trim()) {
      return {
        type: 'dialogue',
        user: dialogue.user.trim(),
        assistant: dialogue.assistant.trim()
      };
    }

    // Fallback to simple text
    return {
      type: 'simple',
      content: content
    };
  };

  // Handle summary click with enhanced feedback
  const handleSummaryClick = (summary) => {
    // Provide visual feedback
    const summaryId = summary.summary_id || summary.id;
    if (summaryId) {
      // Brief highlight effect
      setHoveredSummary(summaryId);
      setTimeout(() => setHoveredSummary(null), 200);
    }

    // Call parent handler
    onSelect(summary);
  };

  // Render dialogue-style summary
  const renderDialogueSummary = (parsedContent, summary) => {
    const summaryId = summary.summary_id || summary.id;
    const isHovered = hoveredSummary === summaryId;

    return (
      <div
        key={summaryId || Math.random()}
        data-testid={`summary-${summaryId}`}
        data-summary-id={summaryId}
        tabIndex={0}
        role="listitem"
        aria-current={isHovered ? 'true' : 'false'}
        onClick={() => handleSummaryClick(summary)}
        onMouseEnter={() => setHoveredSummary(summaryId)}
        onMouseLeave={() => setHoveredSummary(null)}
        onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); handleSummaryClick(summary); } }}
        style={{
          cursor: 'pointer',
          marginBottom: 12,
          border: '1px solid #e0e0e0',
          borderRadius: 8,
          overflow: 'hidden',
          transition: 'all 0.2s ease',
          transform: isHovered ? 'translateY(-1px)' : 'translateY(0)',
          boxShadow: isHovered ? '0 2px 8px rgba(0,0,0,0.1)' : '0 1px 3px rgba(0,0,0,0.05)'
        }}
      >
        {/* Summary header */}
        <div style={{
          padding: '8px 12px',
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #e0e0e0',
          fontSize: 12,
          color: '#666',
          fontWeight: 500
        }}>
          {summary.label || summary.title || `摘要 ${summaryId || ''}`}
        </div>

        {/* Dialogue content */}
        <div style={{ padding: 8 }}>
          {parsedContent.user && (
            <div style={{
              background: '#e3f2fd',
              padding: 8,
              borderRadius: 6,
              marginBottom: 4,
              border: '1px solid #bbdefb'
            }}>
              <div style={{ fontSize: 11, color: '#1976d2', fontWeight: 600, marginBottom: 4 }}>
                👤 用户
              </div>
              <div style={{ fontSize: 13, lineHeight: 1.4, whiteSpace: 'pre-wrap' }}>
                {parsedContent.user}
              </div>
            </div>
          )}

          {parsedContent.assistant && (
            <div style={{
              background: '#f3e5f5',
              padding: 8,
              borderRadius: 6,
              marginBottom: 4,
              border: '1px solid #e1bee7'
            }}>
              <div style={{ fontSize: 11, color: '#7b1fa2', fontWeight: 600, marginBottom: 4 }}>
                🤖 助手
              </div>
              <div style={{ fontSize: 13, lineHeight: 1.4, whiteSpace: 'pre-wrap' }}>
                {parsedContent.assistant}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render simple summary (fallback)
  const renderSimpleSummary = (parsedContent, summary) => {
    const summaryId = summary.summary_id || summary.id;
    const isHovered = hoveredSummary === summaryId;

    return (
      <div
        key={summaryId || Math.random()}
        data-testid={`summary-${summaryId}`}
        data-summary-id={summaryId}
        tabIndex={0}
        role="listitem"
        aria-current={isHovered ? 'true' : 'false'}
        onClick={() => handleSummaryClick(summary)}
        onMouseEnter={() => setHoveredSummary(summaryId)}
        onMouseLeave={() => setHoveredSummary(null)}
        onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); handleSummaryClick(summary); } }}
        style={{
          cursor: 'pointer',
          padding: 12,
          borderRadius: 8,
          background: isHovered ? '#f5f5f5' : '#fafafa',
          marginBottom: 8,
          border: '1px solid #e0e0e0',
          transition: 'all 0.2s ease',
          transform: isHovered ? 'translateY(-1px)' : 'translateY(0)',
          boxShadow: isHovered ? '0 2px 8px rgba(0,0,0,0.1)' : '0 1px 3px rgba(0,0,0,0.05)'
        }}
      >
        <div style={{ fontSize: 12, color: '#666', marginBottom: 6, fontWeight: 500 }}>
          {summary.label || summary.title || `摘要 ${summaryId || ''}`}
        </div>
        <div style={{ fontSize: 13, lineHeight: 1.4, whiteSpace: 'pre-wrap' }}>
          {parsedContent.content}
        </div>
      </div>
    );
  };

  // Keyboard navigation for summaries: ArrowUp / ArrowDown to move focus between summary items.
  // Attach a keydown handler on the summary pane container and make it focusable (tabIndex=0).
  const handleContainerKeyDown = (e) => {
    if (e.key !== 'ArrowUp' && e.key !== 'ArrowDown') return;

    const targetTag = e.target && e.target.tagName ? e.target.tagName.toUpperCase() : '';
    const editableTags = ['INPUT', 'TEXTAREA', 'SELECT'];
    if (editableTags.includes(targetTag)) return;

    const ids = summaries.map(s => s.summary_id || s.id);
    let currentIndex = ids.indexOf(hoveredSummary);
    if (currentIndex === -1) {
      const active = document.activeElement;
      if (active && active.dataset && active.dataset.summaryId) {
        currentIndex = ids.indexOf(active.dataset.summaryId);
      }
    }

    const dir = e.key === 'ArrowDown' ? 1 : -1;
    let targetIndex = currentIndex + dir;
    if (targetIndex < 0) targetIndex = 0;
    if (targetIndex >= ids.length) targetIndex = ids.length - 1;

    const targetId = ids[targetIndex];
    if (targetId) {
      const el = document.querySelector(`[data-testid="summary-${targetId}"]`);
      if (el) {
        el.focus();
        // provide brief hover/highlight feedback
        setHoveredSummary(targetId);
        setTimeout(() => setHoveredSummary(null), 300);
      }
      e.preventDefault();
      e.stopPropagation();
    }
  };
  // Esc key handler: when focus is not inside an input/textarea/select, clear hovered summary
  React.useEffect(() => {
    function onKeyDown(e) {
      if (e.key !== 'Escape') return;

      const targetTag = e.target && e.target.tagName ? e.target.tagName.toUpperCase() : '';
      const editableTags = ['INPUT', 'TEXTAREA', 'SELECT'];

      // If focus is inside an editable control, do nothing
      if (editableTags.includes(targetTag)) return;

      // If the pane is not visible, ignore
      const root = document.querySelector('.summary-pane');
      if (!root || root.offsetParent === null) return;

      // Clear hovered state and (if provided) call onSelect with null to signal deselect
      try {
        setHoveredSummary(null);
        // If parent wants to respond to deselect, call with null (optional)
        if (typeof onSelect === 'function') {
          // do not pass a summary, just notify deselect
          onSelect(null);
        }
      } catch (err) {
        // noop
      }
    }

    window.addEventListener('keydown', onKeyDown, true);
    return () => window.removeEventListener('keydown', onKeyDown, true);
  }, [onSelect]);
  return (
    <div className="summary-pane" style={{
      padding: 12,
      borderLeft: '1px solid #eee',
      height: '100%',
      overflow: 'auto',
      backgroundColor: '#fafafa'
    }}>
      <div style={{
        marginBottom: 12,
        fontWeight: 'bold',
        fontSize: 16,
        color: '#333',
        borderBottom: '2px solid #e0e0e0',
        paddingBottom: 8
      }}>
        📋 对话摘要
      </div>

      {summaries.length === 0 ? (
        <div style={{
          color: '#999',
          textAlign: 'center',
          padding: 20,
          fontSize: 14
        }}>
          暂无摘要
        </div>
      ) : (
        summaries.map(summary => {
          const parsedContent = parseSummaryContent(summary);

          if (parsedContent.type === 'dialogue') {
            return renderDialogueSummary(parsedContent, summary);
          } else {
            return renderSimpleSummary(parsedContent, summary);
          }
        })
      )}
    </div>
  );
}