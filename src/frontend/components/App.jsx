import React, { useState, useRef, useEffect } from 'react';
import ConversationPane from './ConversationPane.jsx';
import SummaryPane from './SummaryPane.jsx';
import LoadingSpinner from './LoadingSpinner.jsx';
import ErrorBoundary, { useErrorHandler } from './ErrorBoundary.jsx';

/**
 * App - 主应用容器
 *
 * - 将 ConversationPane 与 SummaryPane 组合为左右双栏（桌面）/单栏（移动）布局
 * - 管理 messages 与 summaries 的共享状态
 * - 实现 Summary 点击跳转到对应 message 的逻辑（通过 ConversationPane 暴露的 ref）
 *
 * 参考文件：
 * - [`src/frontend/components/ConversationPane.jsx`](src/frontend/components/ConversationPane.jsx:1)
 * - [`src/frontend/components/SummaryPane.jsx`](src/frontend/components/SummaryPane.jsx:1)
 */
export default function App() {
  // 状态管理
  const [messages, setMessages] = useState([]);
  const [summaries, setSummaries] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingText, setLoadingText] = useState('正在加载数据...');

  // 当前选用的 topic id（可扩展为从路由或顶部导航选择）
  const [currentTopicId] = useState(1);

  // 错误处理
  const { error, resetError, captureError } = useErrorHandler();
  
  // 在挂载时从后端拉取 conversation 与 summaries
  useEffect(() => {
    let mounted = true;
    async function loadData() {
      try {
        setIsLoading(true);
        setLoadingText('正在加载对话数据...');

        // 拉取消息（API: GET /api/topics/:topic_id/conversation 或 /messages）
        const convRes = await fetch(`/api/topics/${currentTopicId}/conversation?limit=200`);
        if (convRes.ok) {
          const convJson = await convRes.json();
          // 后端返回结构在不同端点可能差异，尝试兼容常见字段
          const msgs = convJson.results || convJson.items || convJson.messages || convJson.data || convJson || [];
          if (mounted) {
            setMessages(Array.isArray(msgs) ? msgs.map(m => ({
              message_id: m.message_id || m.id,
              sender: m.role || m.sender || m.role,
              content: m.content || m.snippet || m.text || ''
            })) : []);
          }
        }
      } catch (e) {
        console.error('加载 conversation 失败', e);
        if (mounted) {
          captureError(new Error(`加载对话失败: ${e.message}`));
        }
      }

      try {
        setLoadingText('正在加载摘要数据...');

        // 拉取摘要（API: GET /api/topics/:topic_id/summaries 或 /summary）
        const sumsRes = await fetch(`/api/topics/${currentTopicId}/summaries`);
        if (sumsRes.ok) {
          const sumsJson = await sumsRes.json();
          const items = sumsJson.results || sumsJson.items || sumsJson || [];
          if (mounted) {
            setSummaries(Array.isArray(items) ? items.map(s => ({
              summary_id: s.summary_id || s.id || s.message_id,
              title: s.title || s.label || s.label,
              text: s.summary || s.text || s.snippet || '',
              related_message_id: (s.click_metadata && s.click_metadata.message_id) || s.message_id || s.related_message_id || null
            })) : []);
          }
        } else {
          // 有些实现使用 /summary 返回 recent summaries
          const sumsRes2 = await fetch(`/api/topics/${currentTopicId}/summary`);
          if (sumsRes2.ok) {
            const sumsJson2 = await sumsRes2.json();
            const items2 = sumsJson2.results || sumsJson2.items || sumsJson2 || [];
            if (mounted) {
              setSummaries(Array.isArray(items2) ? items2.map(s => ({
                summary_id: s.summary_id || s.id || s.message_id,
                title: s.title || s.label || `摘要 ${s.summary_id || s.id || ''}`,
                text: s.summary || s.text || s.snippet || '',
                related_message_id: (s.click_metadata && s.click_metadata.message_id) || s.message_id || s.related_message_id || null
              })) : []);
            }
          }
        }
      } catch (e) {
        console.error('加载 summaries 失败', e);
        if (mounted) {
          captureError(new Error(`加载摘要失败: ${e.message}`));
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    }
    loadData();
    return () => { mounted = false; };
  }, [currentTopicId, captureError]);

  // ref 用于接收 ConversationPane 暴露的 scrollToMessage 函数
  // ConversationPane 支持 ref 对象：onScrollToMessage.current = scrollToMessage
  const scrollRef = useRef(null);

  // 将新消息添加到 messages（示例 onSend 回调）
  async function handleSend(text) {
    try {
      const newMessage = {
        message_id: `m${Date.now()}`,
        sender: 'user',
        content: text
      };
      setMessages(prev => [...prev, newMessage]);

      // 这里可以添加发送到后端的逻辑
      // 例如：await fetch('/api/messages', { method: 'POST', body: JSON.stringify(newMessage) })
    } catch (e) {
      console.error('发送消息失败', e);
      captureError(new Error(`发送消息失败: ${e.message}`));
    }
  }

  // 当 Summary 被选中时：尝试使用 related_message_id 或从 summary 内容推断目标 message_id
  function handleSummarySelect(summary) {
    let targetMessageId = summary.related_message_id || summary.message_id || summary.target_message_id;

    // 如果 summary 中没有直接字段，尝试从文本里查找已知 message 内容（简单匹配）
    if (!targetMessageId) {
      for (const m of messages) {
        if (summary.text && summary.text.includes(m.content)) {
          targetMessageId = m.message_id || m.id;
          break;
        }
      }
    }

    if (targetMessageId && scrollRef && scrollRef.current) {
      // scrollRef.current 应为 ConversationPane 暴露的 scrollTo(messageId) 函数
      try {
        scrollRef.current(targetMessageId, { highlight: true, behavior: 'smooth' });
      } catch (e) {
        // 兼容性回退：如果 scrollRef.current 为一个对象（ref 风格）
        if (typeof scrollRef.current === 'object' && scrollRef.current.current) {
          scrollRef.current.current(targetMessageId, { highlight: true, behavior: 'smooth' });
        }
      }
    }
  }

  // 简单响应式样式：左右两列在宽屏显示，窄屏垂直堆叠
  const containerStyle = {
    display: 'flex',
    flexDirection: 'row',
    height: '100vh',
    width: '100%',
  };

  const leftStyle = {
    flex: 1,
    minWidth: 0,
    display: 'flex',
    flexDirection: 'column'
  };

  const rightStyle = {
    width: 360,
    borderLeft: '1px solid #eee',
    overflow: 'auto',
    backgroundColor: '#fafafa'
  };

  // media query fallback: if window is narrow, stack columns
  const [isNarrow, setIsNarrow] = useState(false);
  useEffect(() => {
    function onResize() {
      setIsNarrow(window.innerWidth < 800);
    }
    onResize();
    window.addEventListener('resize', onResize);
    return () => window.removeEventListener('resize', onResize);
  }, []);

  // 如果有错误，显示错误界面
  if (error) {
    return (
      <div style={{
        height: '100vh',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          padding: 20,
          margin: 20,
          border: '1px solid #ff6b6b',
          borderRadius: 8,
          backgroundColor: '#fff5f5',
          color: '#c92a2a',
          maxWidth: 500
        }}>
          <h3 style={{ margin: '0 0 12px 0', color: '#c92a2a' }}>
            😵 应用加载失败
          </h3>
          <p style={{ margin: '0 0 16px 0', color: '#666' }}>
            {error.message || '应用遇到了问题，请尝试刷新页面。'}
          </p>
          <div style={{ display: 'flex', gap: 8 }}>
            <button
              onClick={resetError}
              style={{
                padding: '8px 16px',
                backgroundColor: '#3498db',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                fontSize: 14
              }}
            >
              重试
            </button>
            <button
              onClick={() => window.location.reload()}
              style={{
                padding: '8px 16px',
                backgroundColor: '#95a5a6',
                color: 'white',
                border: 'none',
                borderRadius: 4,
                cursor: 'pointer',
                fontSize: 14
              }}
            >
              刷新页面
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 如果正在加载，显示加载界面
  if (isLoading) {
    return (
      <div style={{
        height: '100vh',
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        gap: 16
      }}>
        <LoadingSpinner size={32} />
        <div style={{ color: '#666', fontSize: 16 }}>{loadingText}</div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div style={{ height: '100vh', width: '100%' }}>
        <div style={isNarrow ? { ...containerStyle, flexDirection: 'column' } : containerStyle}>
          <div style={leftStyle}>
            <ErrorBoundary>
              <ConversationPane
                messages={messages}
                onSend={handleSend}
                onScrollToMessage={scrollRef}
                enableLazyLoading={false}
                autoScrollToBottom={true}
              />
            </ErrorBoundary>
          </div>

          <div style={isNarrow ? { ...rightStyle, width: '100%', borderLeft: 'none', borderTop: '1px solid #eee' } : rightStyle}>
            <ErrorBoundary>
              <SummaryPane
                summaries={summaries}
                onSelect={handleSummarySelect}
              />
            </ErrorBoundary>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}
